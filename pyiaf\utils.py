"""
Utility functions for IAF-FBO algorithm
"""

import numpy as np
from scipy.spatial.distance import pdist, squareform
from scipy.stats import norm
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')


def lhs_classic(n_samples, n_features, random_state=None):
    """
    Latin Hypercube Sampling
    
    Args:
        n_samples: Number of samples
        n_features: Number of features/dimensions
        random_state: Random seed
    
    Returns:
        samples: Array of shape (n_samples, n_features) with values in [0, 1]
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    samples = np.zeros((n_samples, n_features))
    
    for i in range(n_features):
        # Create equally spaced intervals
        intervals = np.linspace(0, 1, n_samples + 1)
        # Sample uniformly within each interval
        samples[:, i] = np.random.uniform(intervals[:-1], intervals[1:])
        # Shuffle the samples for this dimension
        np.random.shuffle(samples[:, i])
    
    return samples


def rbf_kernel(x1, x2, theta, nugget=1e-10):
    """
    RBF (Gaussian) kernel function
    
    Args:
        x1, x2: Input points
        theta: Hyperparameters (length scales)
        nugget: Small value for numerical stability
    
    Returns:
        Kernel value
    """
    if x1.ndim == 1:
        x1 = x1.reshape(1, -1)
    if x2.ndim == 1:
        x2 = x2.reshape(1, -1)
    
    # Compute squared distances
    diff = x1[:, np.newaxis, :] - x2[np.newaxis, :, :]
    weighted_diff = diff / theta
    sq_dist = np.sum(weighted_diff**2, axis=2)
    
    # RBF kernel
    K = np.exp(-0.5 * sq_dist)
    
    # Add nugget for numerical stability
    if x1.shape[0] == x2.shape[0] and np.allclose(x1, x2):
        K += nugget * np.eye(x1.shape[0])
    
    return K


def acquisition_function(mu, sigma, y_best, af_type='LCB', kappa=2.0):
    """
    Compute acquisition function values
    
    Args:
        mu: Predicted mean
        sigma: Predicted standard deviation
        y_best: Best observed value so far
        af_type: Type of acquisition function ('UCB', 'LCB', 'EI')
        kappa: Exploration parameter
    
    Returns:
        Acquisition function values
    """
    if af_type == 'UCB':
        return mu + kappa * sigma
    elif af_type == 'LCB':
        return mu - kappa * sigma
    elif af_type == 'EI':
        # Expected Improvement
        improvement = y_best - mu
        Z = improvement / (sigma + 1e-9)
        ei = improvement * norm.cdf(Z) + sigma * norm.pdf(Z)
        return -ei  # Negative because we want to maximize EI
    else:
        raise ValueError(f"Unknown acquisition function type: {af_type}")


# normalize_data and denormalize_data functions removed - not used in core algorithm


def create_pairwise_data(X, y):
    """
    Create pairwise comparison data for classifier training
    
    Args:
        X: Input features
        y: Target values (acquisition function values)
    
    Returns:
        X_pairs: Pairwise input features
        y_pairs: Pairwise labels (-1, 0, 1)
    """
    n_samples = len(X)
    n_pairs = n_samples * (n_samples - 1) // 2
    
    X_pairs = np.zeros((n_pairs, 2 * X.shape[1]))
    y_pairs = np.zeros(n_pairs)
    
    idx = 0
    for i in range(n_samples):
        for j in range(i + 1, n_samples):
            X_pairs[idx] = np.concatenate([X[i], X[j]])
            
            if y[i] > y[j]:
                y_pairs[idx] = 1
            elif y[i] < y[j]:
                y_pairs[idx] = -1
            else:
                y_pairs[idx] = 0
            
            idx += 1
    
    return X_pairs, y_pairs


# compute_similarity_matrix function removed - not used in core algorithm


def kmeans_clustering(weight_vectors, n_clusters, max_iters=100, random_state=None):
    """
    Simple K-means clustering implementation
    
    Args:
        weight_vectors: Array of weight vectors
        n_clusters: Number of clusters
        max_iters: Maximum iterations
        random_state: Random seed
    
    Returns:
        cluster_labels: Cluster assignment for each point
        centroids: Cluster centroids
    """
    if random_state is not None:
        np.random.seed(random_state)
    
    n_samples, n_features = weight_vectors.shape
    
    # Initialize centroids randomly
    centroids = weight_vectors[np.random.choice(n_samples, n_clusters, replace=False)]
    
    for _ in range(max_iters):
        # Assign points to closest centroid
        distances = np.sqrt(((weight_vectors - centroids[:, np.newaxis])**2).sum(axis=2))
        cluster_labels = np.argmin(distances, axis=0)
        
        # Update centroids
        new_centroids = np.array([weight_vectors[cluster_labels == k].mean(axis=0) 
                                 for k in range(n_clusters)])
        
        # Check for convergence
        if np.allclose(centroids, new_centroids):
            break
        
        centroids = new_centroids
    
    return cluster_labels, centroids


def one_hot_encode(labels, n_classes=None):
    """
    One-hot encode labels
    
    Args:
        labels: Integer labels
        n_classes: Number of classes (inferred if None)
    
    Returns:
        One-hot encoded labels
    """
    if n_classes is None:
        n_classes = len(np.unique(labels))
    
    one_hot = np.zeros((len(labels), n_classes))
    for i, label in enumerate(labels):
        if label == 1:
            one_hot[i, 0] = 1
        elif label == -1:
            one_hot[i, 1] = 1
        # label == 0 remains all zeros
    
    return one_hot


def one_hot_decode(one_hot):
    """
    Decode one-hot encoded labels
    
    Args:
        one_hot: One-hot encoded labels
    
    Returns:
        Integer labels
    """
    labels = np.zeros(len(one_hot))
    max_indices = np.argmax(one_hot, axis=1)
    
    labels[max_indices == 0] = 1
    labels[max_indices == 1] = -1
    # max_indices == other values remain 0
    
    return labels.astype(int)
