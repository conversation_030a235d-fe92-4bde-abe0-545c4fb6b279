function [ T ] = <PERSON>erstrass_( X , M,opt )
%ROSENBROCK20 Summary of this function goes here
% %%%%%Shift<PERSON><PERSON><PERSON>’s Function [-100 100] F6

T = [];

for t = 1:size(X,1)
    x = X(t,:);

    var = x;
    D = length(var);
    var = (M*(var-opt)')';
    a = 0.5;
    b = 3;
    kmax = 20;
    obj = 0;
    for i = 1:D
        for k = 0:kmax
            obj = obj + a^k*cos(2*pi*b^k*(var(i)+0.5));
        end
    end
    for k = 0:kmax
        obj = obj - D*a^k*cos(2*pi*b^k*0.5);
    end


    T(t,:) = obj;

end
end

